<?php

namespace Modules\CriticalAcclaim\app\Repositories;

use Modules\CriticalAcclaim\app\Models\CriticalAcclaim;
use App\Repositories\Admin\Repository;

class CriticalAcclaimRepository extends Repository
{
    /**
     * CriticalAcclaimRepository constructor.
     * @param CriticalAcclaim $model
     */
    public function __construct(CriticalAcclaim $model)
    {
        $this->model = $model;
    }

    /**
     * Handle bulk actions for critical acclaims
     */
    public function bulkAction($ids, $status, $actionType): array
    {
        $type = 'success';

        switch ($actionType) {
            case 'delete':
                $this->model->whereIn('id', $ids)->delete();
                $message = __('criticalacclaim::criticalacclaim.critical_acclaim_delete_successfully');
                break;

            default:
                $type = 'error';
                $message = __('criticalacclaim::criticalacclaim.something_wrong');
                break;
        }

        return [
            'type' => $type,
            'message' => $message
        ];
    }

    /**
     * Get all critical acclaims with author relationship
     */
    public function getWithAuthor()
    {
        return $this->model->with('author')->get();
    }

    /**
     * Get critical acclaim by ID with author relationship
     */
    public function getWithAuthorById($id)
    {
        return $this->model->with('author')->findOrFail($id);
    }
}
