<?php

namespace Modules\CriticalAcclaim\app\DataGrids;

use Illuminate\Support\Facades\Auth;
use Indianic\LaravelDataGrid\LaravelDataGrid;
use Illuminate\Support\Facades\DB;
use Modules\CriticalAcclaim\app\Models\CriticalAcclaim;

class CriticalAcclaimDataGrid extends LaravelDataGrid
{
    public $guard = 'admin';

    /**
     * Define unique table id
     * @var mixed $uniqueID
     */
    public $uniqueID = 'criticalacclaim';

    /**
     * Define how many rows you want to display on a page
     * @var int $rowPerPage
     */
    public $recordsPerPage = 10;

    /**
     * Define mysql column name which you want to default on sorting
     * @var string $sortBy
     */
    public $sortBy = 'id';

    /**
     * Define default soring direction
     * Example: ASC | DESC
     * @var string $sortByDirection
     */
    public $sortByDirection = 'DESC';

    /**
     * Set download file prefix or set false
     * @var mixed
     */
    protected $downloadFilePrefix = 'criticalacclaim';

    // Constants for translations
    protected const HEADING = 'criticalacclaim::criticalacclaim.heading';
    protected const SUB_HEADING = 'criticalacclaim::criticalacclaim.sub_heading';
    protected const SUMMARY = 'criticalacclaim::criticalacclaim.summary';
    protected const AUTHOR = 'criticalacclaim::criticalacclaim.author';
    protected const CREATED_AT = 'criticalacclaim::criticalacclaim.created_at';
    protected const ACTION = 'criticalacclaim::criticalacclaim.action';

    public function resource()
    {
        return CriticalAcclaim::select(
                    'critical_acclaims.id',
                    'critical_acclaims.created_at',
                    DB::raw("JSON_EXTRACT(critical_acclaims.heading, '$.en') as heading"),
                    DB::raw("JSON_EXTRACT(critical_acclaims.sub_heading, '$.en') as sub_heading"),
                    DB::raw("JSON_EXTRACT(critical_acclaims.summary, '$.en') as summary"),
                    DB::raw("JSON_EXTRACT(authors.name, '$.en') as author_name"),
                    DB::raw("COALESCE(JSON_EXTRACT(critical_acclaims.heading, '$.en'), JSON_EXTRACT(critical_acclaims.heading, '$.ar')) as heading_with_fallback"),
                )
                ->leftJoin('authors', 'authors.id', '=', 'critical_acclaims.author_id');
    }

    public function columns(): array
    {
        return [
            'checkbox'      => '<input class="select_all checkbox" type="checkbox" />',
            'heading'       => __(self::HEADING),
            'sub_heading'   => __(self::SUB_HEADING),
            'summary'       => __(self::SUMMARY),
            'author_name'   => __(self::AUTHOR),
            'created_at'    => __(self::CREATED_AT),
            'action'        => __(self::ACTION)
        ];
    }

    public function mapDBColumns(): array
    {
        return [
            'heading' => 'critical_acclaims.heading',
            'sub_heading' => 'critical_acclaims.sub_heading',
            'summary' => 'critical_acclaims.summary',
            'author_name' => 'authors.name',
            'created_at' => 'critical_acclaims.created_at'
        ];
    }

    public function sortableColumns(): array
    {
        return [
            'heading',
            'sub_heading',
            'author_name',
            'created_at'
        ];
    }

    public function downloadableColumns(): array
    {
        return [
            'heading'       => __(self::HEADING),
            'sub_heading'   => __(self::SUB_HEADING),
            'summary'       => __(self::SUMMARY),
            'author_name'   => __(self::AUTHOR),
            'created_at'    => __(self::CREATED_AT)
        ];
    }

    public function gloablSearchableColumns(): array
    {
        return [
            'heading'       => 'string',
            'sub_heading'   => 'string',
            'summary'       => 'string',
            'author_name'   => 'string',
            'created_at'    => 'string'
        ];
    }

    public function searchableColumns(): array
    {
        return [
            'heading'       => 'string',
            'sub_heading'   => 'string',
            'summary'       => 'string',
            'author_name'   => 'string',
            'created_at'    => 'date'
        ];
    }

    public function getBulkAction(): array
    {
        return [
            'delete' => [
                'class' => 'danger',
                'title' => __('criticalacclaim::criticalacclaim.delete'),
                'module' => 'CriticalAcclaim',
                'type' => 'delete'
            ],
        ];
    }

    public function getColumnHeading(array $data): string
    {
        return '<div class="text-truncate" style="max-width: 200px;" title="' . htmlspecialchars($data['heading']) . '">' . htmlspecialchars($data['heading']) . '</div>';
    }

    public function getColumnSubHeading(array $data): string
    {
        return '<div class="text-truncate" style="max-width: 200px;" title="' . htmlspecialchars($data['sub_heading']) . '">' . htmlspecialchars($data['sub_heading']) . '</div>';
    }

    public function getColumnSummary(array $data): string
    {
        return '<div class="text-truncate" style="max-width: 250px;" title="' . htmlspecialchars($data['summary']) . '">' . htmlspecialchars($data['summary']) . '</div>';
    }

    public function getColumnAuthorName(array $data): string
    {
        $authorName = $data['author_name'] ?? 'N/A';
        $authorName = trim($authorName, '"');
        return '<span class="badge bg-info">' . htmlspecialchars($authorName) . '</span>';
    }

    public function getColumnCreatedAt(array $data): string
    {
        return adminDateTimeFormatShow($data['created_at']);
    }

    public function getColumnAction(array $data): string
    {
        $return = '';

        if (Auth::user()->hasPermission('update-critical-acclaim')) {
            $return .= '<a class="me-3" href="' . route('admin.critical-acclaims.edit', encrypt($data['id'])) . '">
            <span class="inic inic-edit fs-18" data-bs-toggle="tooltip" title="Edit"></span></a>';
        }

        if (Auth::user()->hasPermission('read-critical-acclaim')) {
            $return .= '<a class="me-3" href="' . route('admin.critical-acclaims.show', encrypt($data['id'])) . '">
            <span class="inic inic-eye fs-18" data-bs-toggle="tooltip" title="View"></span></a>';
        }

        if (Auth::user()->hasPermission('delete-critical-acclaim')) {
            $return .= '<a class="delete-grid-row" href="javascript:void(0)" data-grid="inic_grid_' . $this->uniqueID . '"
            data-url="' . route('admin.critical-acclaims.destroy', encrypt($data['id'])) . '">
            <span class="inic inic-bin fs-18" data-bs-toggle="tooltip" title="Delete"></span></a>';
        }

        return $return;
    }

    public function getColumnCheckbox(array $data): string
    {
        return '<input class="check_approval" type="checkbox" value="' . $data['id'] . '" />';
    }
}
