<?php

namespace Modules\CriticalAcclaim\app\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Spatie\Translatable\HasTranslations;
use Modules\Author\app\Models\Author;

class CriticalAcclaim extends Model
{
    use HasFactory, HasTranslations;

    /**
     * The attributes that are translatable.
     *
     * @var array
     */
    public $translatable = ['heading', 'sub_heading', 'summary'];

    /**
     * The default locale to use for translations.
     *
     * @return string
     */
    public function getDefaultLocale(): string
    {
        return 'en';
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'author_id',
        'heading',
        'sub_heading',
        'summary',
    ];

    /**
     * The attributes that are guarded.
     *
     * @var array
     */
    protected $guarded = ['id'];

    /**
     * Get the author that owns the critical acclaim.
     */
    public function author(): BelongsTo
    {
        return $this->belongsTo(Author::class);
    }

    /**
     * Route model binding with encryption.
     */
    public function resolveRouteBinding($value, $field = null): self
    {
        return $this->findOrFail(decrypt($value));
    }
}
