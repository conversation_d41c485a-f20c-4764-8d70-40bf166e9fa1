<?php

namespace Modules\CriticalAcclaim\app\Http\Requests;

use App\Rules\StripTags;
use Illuminate\Foundation\Http\FormRequest;

class CriticalAcclaimRequest extends FormRequest
{
    protected const STRING_REGEX = 'regex:/^[a-zA-Z0-9\s\-\.]+$/';
    protected const STRING = 'string';

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $maxSize = 'max:255';
        $longMaxSize = 'max:10000';

        $rules = [
            // Translatable fields
            'heading' => ['required', 'array'],
            'heading.en' => ['required', self::STRING, $maxSize],
            'heading.ar' => ['nullable', self::STRING, $maxSize],

            'sub_heading' => ['required', 'array'],
            'sub_heading.en' => ['required', self::STRING, $maxSize],
            'sub_heading.ar' => ['nullable', self::STRING, $maxSize],

            'summary' => ['required', 'array'],
            'summary.en' => ['required', self::STRING, $longMaxSize],
            'summary.ar' => ['nullable', self::STRING, $longMaxSize],

            // Author relationship
            'author_id' => ['required', 'exists:authors,id'],
        ];

        return $rules;
    }

    public function messages(): array
    {
        return [
            'heading.required' => __('criticalacclaim::criticalacclaim.heading_required'),
            'heading.en.required' => __('criticalacclaim::criticalacclaim.heading_required'),
            'heading.ar.required' => __('criticalacclaim::criticalacclaim.heading_required'),

            'sub_heading.required' => __('criticalacclaim::criticalacclaim.sub_heading_required'),
            'sub_heading.en.required' => __('criticalacclaim::criticalacclaim.sub_heading_required'),
            'sub_heading.ar.required' => __('criticalacclaim::criticalacclaim.sub_heading_required'),

            'summary.required' => __('criticalacclaim::criticalacclaim.summary_required'),
            'summary.en.required' => __('criticalacclaim::criticalacclaim.summary_required'),
            'summary.ar.required' => __('criticalacclaim::criticalacclaim.summary_required'),

            'author_id.required' => __('criticalacclaim::criticalacclaim.author_required'),
            'author_id.exists' => __('criticalacclaim::criticalacclaim.author_invalid'),
        ];
    }
}
